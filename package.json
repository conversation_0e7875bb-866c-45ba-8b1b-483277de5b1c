{"name": "voxana", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "db:push": "drizzle-kit push", "db:cleanup": "tsx src/db/scripts/cleanup.ts", "db:postrun": "tsx src/db/scripts/postScripts.ts", "db:deploy": "npm run db:cleanup && npm run db:push && npm run db:postrun"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.52.1", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-router": "^1.129.8", "@tanstack/react-router-devtools": "^1.129.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "lucide-react": "^0.525.0", "pg": "^8.16.3", "postgres": "^3.4.7", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tanstack/router-plugin": "^1.129.8", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "drizzle-kit": "^0.31.4", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tw-animate-css": "^1.3.5", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vite-plugin-pwa": "^1.0.1"}}