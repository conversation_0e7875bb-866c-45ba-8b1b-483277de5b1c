import {pgTable, text, timestamp, pgRole, pgPolicy, uuid} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';

export const usersTable = pgTable('user_info2', {
  id: uuid('id').primaryKey().notNull().references(() => sql`auth.users(id)`, {
    onDelete: 'cascade',
    onUpdate: 'cascade'
  }),
  display_name: text('display_name').notNull(),
  primary_email: text('primary_email').notNull().unique(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at')
    .notNull()
    .$onUpdate(() => new Date()),
}/*, () => [
  pgPolicy('allow_all_operations', {
    as: 'permissive',
    for: 'all',
    to: 'public',
    using: sql`true`,
    withCheck: sql`true`,
  }),
]*/);

export type InsertUser = typeof usersTable.$inferInsert;
export type SelectUser = typeof usersTable.$inferSelect;
