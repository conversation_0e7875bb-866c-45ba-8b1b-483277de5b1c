import {pgTable, text, timestamp, pgRole, pgPolicy, uuid, pgSchema} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';

// Define the auth schema
export const authSchema = pgSchema('auth');

// Reference to auth.users table
export const authUsers = authSchema.table('users', {
  id: uuid('id').primaryKey().notNull(),
});

export const usersTable = pgTable('user_info2', {
  id: uuid('id').primaryKey().notNull().references(() => authUsers.id, {
    onDelete: 'cascade',
    onUpdate: 'cascade'
  }),
  display_name: text('display_name').notNull(),
  primary_email: text('primary_email').notNull().unique(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at')
    .notNull()
    .$onUpdate(() => new Date()),
}/*, () => [
  pgPolicy('allow_all_operations', {
    as: 'permissive',
    for: 'all',
    to: 'public',
    using: sql`true`,
    withCheck: sql`true`,
  }),
]*/);

export type InsertUser = typeof usersTable.$inferInsert;
export type SelectUser = typeof usersTable.$inferSelect;
