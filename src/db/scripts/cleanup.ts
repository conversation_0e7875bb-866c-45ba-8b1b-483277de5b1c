import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import { sql } from 'drizzle-orm';
import postgres from 'postgres';

config({ path: '.env' });

const dropThings = sql`
-- Drop roles
DROP ROLE IF EXISTS admin;
DROP ROLE IF EXISTS slp;

-- Drop the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Drop the function
DROP FUNCTION IF EXISTS public.handle_new_user;
`;

const createSchema = sql`
CREATE SCHEMA public;
`;

const dropSchema = sql`
DROP SCHEMA public CASCADE;
`;

const main = async () => {
  console.log('Starting database cleanup...');

  const client = postgres(process.env.DATABASE_URL!);
  const db = drizzle({ client });

  try {
    console.log('Drpoping schema');
    await db.execute(dropSchema);

    console.log('Dropping listed things');
    await db.execute(dropThings);

    console.log('Creating schema');
    await db.execute(createSchema);
    console.log('Database cleanup completed successfully!');
  } catch (error) {
    console.error('❌ Database cleanup failed:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

main().catch((err) => {
  console.error("Error:", err);
  process.exit(1);
});